"""
规则数据同步服务 - 重构版本
适配新的三表结构（RuleDetail、RuleTemplate、RuleFieldMetadata）
支持增量同步、压缩传输和错误恢复
"""

import gzip
import json
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Any

from sqlalchemy.orm import Session, selectinload

from core.logging.logging_system import log as logger
from core.rule_cache import RULE_CACHE
from models.database import RuleDetail, RuleDetailStatusEnum, RuleTemplate, RuleTemplateStatusEnum
from rules.rule_registry import rule_registry  # 使用全局实例
from services.rule_detail_service import ServiceError
from services.unified_data_mapping_engine import UnifiedDataMappingEngine


@dataclass
class SyncStatistics:
    """同步统计信息"""

    total_templates: int
    total_details: int
    total_metadata: int
    sync_duration: float
    cache_size_mb: float
    compression_ratio: float
    last_sync_time: float


class RuleDataSyncService:
    """规则数据同步服务 - 重构版本"""

    def __init__(self, session: Session = None, cache_file_path: str = "rules_cache.json.gz"):
        """
        初始化服务

        Args:
            session: 数据库会话（主节点使用）
            cache_file_path: 缓存文件路径
        """
        self.session = session
        self.cache_file_path = Path(cache_file_path)
        self.data_mapping_engine = UnifiedDataMappingEngine()
        self.rule_registry = rule_registry  # 使用全局规则注册表实例

        # 同步配置
        self.compression_level = 6
        self.batch_size = 1000

    def sync_from_database(self, force_full_sync: bool = False) -> SyncStatistics:
        """
        从数据库同步规则数据到本地缓存

        Args:
            force_full_sync: 是否强制全量同步

        Returns:
            SyncStatistics: 同步统计信息

        Raises:
            ServiceException: 当同步失败时
        """
        if not self.session:
            raise ServiceError("数据库会话未初始化，无法从数据库同步", error_code="SESSION_NOT_INITIALIZED")

        start_time = time.perf_counter()

        try:
            logger.debug(f"开始从数据库同步规则数据: force_full={force_full_sync}")

            # 1. 获取同步数据
            sync_data = self._load_data_from_database(force_full_sync)

            # 2. 转换数据格式
            cache_data = self._convert_to_cache_format(sync_data)

            # 3. 保存到缓存文件
            original_size, compressed_size = self._save_cache_file(cache_data)

            # 4. 更新内存缓存
            self._update_memory_cache(cache_data)

            # 5. 构建统计信息
            duration = time.perf_counter() - start_time
            stats = SyncStatistics(
                total_templates=len(sync_data["templates"]),
                total_details=len(sync_data["details"]),
                total_metadata=sum(len(t.get("metadata", [])) for t in sync_data["templates"]),
                sync_duration=duration,
                cache_size_mb=compressed_size / (1024 * 1024),
                compression_ratio=original_size / compressed_size if compressed_size > 0 else 1.0,
                last_sync_time=time.perf_counter(),
            )

            logger.info(
                f"数据库同步完成: templates={stats.total_templates}, "
                f"details={stats.total_details}, metadata={stats.total_metadata}, "
                f"duration={duration:.2f}s, size={stats.cache_size_mb:.2f}MB, "
                f"compression={stats.compression_ratio:.2f}x"
            )

            return stats

        except Exception as e:
            error_msg = f"从数据库同步失败: error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(error_msg, error_code="DATABASE_SYNC_FAILED", details={"error": str(e)}) from None

    def sync_from_cache(self) -> SyncStatistics:
        """
        从本地缓存同步规则数据（为了与sync_from_database保持接口一致性）

        Returns:
            SyncStatistics: 同步统计信息

        Raises:
            ServiceException: 当同步失败时
        """
        return self.load_from_cache()

    def load_from_cache(self) -> SyncStatistics:
        """
        从本地缓存加载规则数据

        Returns:
            SyncStatistics: 加载统计信息

        Raises:
            ServiceException: 当加载失败时
        """
        start_time = time.perf_counter()

        try:
            if not self.cache_file_path.exists():
                raise ServiceError(
                    f"缓存文件不存在: {self.cache_file_path}",
                    error_code="CACHE_FILE_NOT_FOUND",
                    details={"cache_path": str(self.cache_file_path)},
                )

            logger.info(f"开始从缓存加载规则数据: {self.cache_file_path}")

            # 1. 读取缓存文件
            cache_data = self._load_cache_file()

            # 2. 更新内存缓存
            self._update_memory_cache(cache_data)

            # 3. 构建统计信息
            duration = time.perf_counter() - start_time
            file_size = self.cache_file_path.stat().st_size

            stats = SyncStatistics(
                total_templates=len(cache_data.get("templates", [])),
                total_details=len(cache_data.get("details", [])),
                total_metadata=sum(len(t.get("metadata", [])) for t in cache_data.get("templates", [])),
                sync_duration=duration,
                cache_size_mb=file_size / (1024 * 1024),
                compression_ratio=1.0,  # 从缓存加载时无法计算压缩比
                last_sync_time=cache_data.get("sync_time", time.perf_counter()),
            )

            logger.info(
                f"缓存加载完成: templates={stats.total_templates}, "
                f"details={stats.total_details}, metadata={stats.total_metadata}, "
                f"duration={duration:.2f}s, size={stats.cache_size_mb:.2f}MB"
            )

            return stats

        except Exception as e:
            error_msg = f"从缓存加载失败: error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg,
                error_code="CACHE_LOAD_FAILED",
                details={"error": str(e), "cache_path": str(self.cache_file_path)},
            ) from None

    def get_sync_status(self) -> dict[str, Any]:
        """
        获取同步状态信息

        Returns:
            Dict: 同步状态信息
        """
        try:
            status = {
                "cache_file_exists": self.cache_file_path.exists(),
                "cache_file_path": str(self.cache_file_path),
                "memory_cache_size": len(RULE_CACHE),
                "database_available": self.session is not None,
            }

            if self.cache_file_path.exists():
                file_stat = self.cache_file_path.stat()
                status.update(
                    {"cache_file_size_mb": file_stat.st_size / (1024 * 1024), "cache_file_modified": file_stat.st_mtime}
                )

            if self.session:
                try:
                    template_count = self.session.query(RuleTemplate).count()
                    detail_count = self.session.query(RuleDetail).count()
                    status.update({"database_templates": template_count, "database_details": detail_count})
                except Exception as e:
                    status["database_error"] = str(e)

            return status

        except Exception as e:
            logger.error(f"获取同步状态失败: error={e}")
            return {"error": str(e)}

    def _load_data_from_database(self, force_full_sync: bool) -> dict[str, Any]:
        """从数据库加载数据"""
        # 1. 加载规则模板和字段元数据
        templates_query = (
            self.session.query(RuleTemplate)
            .options(selectinload(RuleTemplate.field_metadata))
            .filter(RuleTemplate.status == RuleTemplateStatusEnum.READY)
        )

        templates = templates_query.all()

        # 2. 加载规则明细
        details_query = self.session.query(RuleDetail).filter(RuleDetail.status == RuleDetailStatusEnum.ACTIVE)

        details = details_query.all()

        # 3. 构建同步数据
        sync_data = {
            "templates": [self._template_to_dict(template) for template in templates],
            "details": [self._detail_to_dict(detail) for detail in details],
            "sync_time": time.perf_counter(),
            "sync_type": "full" if force_full_sync else "incremental",
        }

        logger.debug(f"从数据库加载数据: templates={len(templates)}, details={len(details)}")

        return sync_data

    def _template_to_dict(self, template: RuleTemplate) -> dict[str, Any]:
        """将RuleTemplate转换为字典"""
        return {
            "rule_key": template.rule_key,
            "name": template.name,
            "description": template.description,
            "status": template.status.value,
            "metadata": [
                {
                    "field_name": field_metadata.field_name,
                    "display_name": field_metadata.display_name,
                    "field_type": field_metadata.field_type.value if field_metadata.field_type else None,
                    "is_required": field_metadata.is_required,
                    "validation_rules": field_metadata.get_validation_rules(),
                    "default_value": field_metadata.default_value,
                    "description": field_metadata.description,
                    "excel_column_order": field_metadata.excel_column_order,
                }
                for field_metadata in template.field_metadata
            ],
            "created_at": template.created_at.timestamp() if template.created_at else None,
            "updated_at": template.updated_at.timestamp() if template.updated_at else None,
        }

    def _detail_to_dict(self, detail: RuleDetail) -> dict[str, Any]:
        """将RuleDetail转换为字典"""
        detail_dict = detail.to_dict()

        # 转换时间戳
        if detail.created_at:
            detail_dict["created_at"] = detail.created_at.timestamp()
        if detail.updated_at:
            detail_dict["updated_at"] = detail.updated_at.timestamp()

        return detail_dict

    def _convert_to_cache_format(self, sync_data: dict[str, Any]) -> dict[str, Any]:
        """转换为缓存格式（简化版本，兼容还原后的格式）"""
        # 使用简化的格式，与还原后的rule_datasets格式兼容
        cache_data = {
            "version": sync_data.get("sync_time", time.perf_counter()),  # 使用时间戳作为版本
            "rule_datasets": [],
            "export_timestamp": time.perf_counter(),
            "total_count": 0,
            "sync_type": sync_data["sync_type"],
        }

        # 按规则键组织数据
        rules_by_key = {}
        for detail in sync_data["details"]:
            try:
                rule_key = detail.get("rule_key")
                if rule_key:
                    if rule_key not in rules_by_key:
                        rules_by_key[rule_key] = []
                    rules_by_key[rule_key].append(detail)
            except Exception as e:
                logger.warning(f"处理规则明细失败: detail_id={detail.get('id')}, error={e}")

        # 转换为rule_datasets格式
        for rule_key, rule_data_list in rules_by_key.items():
            dataset_entry = {
                "rule_key": rule_key,
                "rule_data": rule_data_list,
                "count": len(rule_data_list),
            }
            cache_data["rule_datasets"].append(dataset_entry)

        cache_data["total_count"] = len(cache_data["rule_datasets"])
        return cache_data

    def _save_cache_file(self, cache_data: dict[str, Any]) -> tuple[int, int]:
        """保存缓存文件"""
        # 序列化数据
        json_data = json.dumps(cache_data, ensure_ascii=False, separators=(",", ":"))
        original_size = len(json_data.encode("utf-8"))

        # 压缩保存
        with gzip.open(self.cache_file_path, "wt", encoding="utf-8", compresslevel=self.compression_level) as f:
            f.write(json_data)

        compressed_size = self.cache_file_path.stat().st_size

        logger.debug(
            f"保存缓存文件: original={original_size}, compressed={compressed_size}"
        )

        return original_size, compressed_size

    def _load_cache_file(self) -> dict[str, Any]:
        """加载缓存文件"""
        with gzip.open(self.cache_file_path, "rt", encoding="utf-8") as f:
            cache_data = json.load(f)

        logger.debug(f"加载缓存文件: version={cache_data.get('version', 'unknown')}")
        return cache_data

    def _update_memory_cache(self, cache_data: dict[str, Any]):
        """更新内存缓存（兼容还原后的格式）"""
        try:
            # 清空现有缓存
            RULE_CACHE.clear()

            # 处理rule_datasets格式
            rule_datasets = cache_data.get("rule_datasets", [])

            for dataset in rule_datasets:
                rule_key = dataset.get("rule_key")
                rule_data_list = dataset.get("rule_data", [])

                if not rule_key or not rule_data_list:
                    continue

                try:
                    # 使用规则注册表创建规则实例
                    rule_class = self.rule_registry.get_rule_class(rule_key)
                    if rule_class:
                        for rule_data in rule_data_list:
                            # 去除实例化时不需要的字段
                            rule_data.pop("id")
                            rule_data.pop("rule_key")
                            rule_data.pop("created_at")
                            rule_data.pop("updated_at")
                            rule_data.pop("status")
                            tmp_rule_data = {}
                            # 将规则特定字段从extra_data中恢复到根级别
                            extended_fields = rule_data.pop("extended_fields")
                            if extended_fields:
                                extended_fields = json.loads(extended_fields)
                                for k, v in extended_fields.items():
                                    if v:
                                        tmp_rule_data[k] = v
                            # 将其他不为空的字段也从 rule_data 中拿出来
                            for key, value in rule_data.items():
                                # 如果字段为空，则说明当前规则不需要这个字段
                                if value:
                                    tmp_rule_data[key] = value

                            # 简单的数据转换，确保基本字段存在
                            instance_data = {
                                # "id": rule_data.get("rule_id", rule_data.get("id", "")),
                                # "name": rule_data.get("rule_name", ""),
                                **tmp_rule_data,  # 包含所有原始数据
                            }
                            rule_instance = rule_class(**instance_data)
                            RULE_CACHE[rule_instance.rule_id] = rule_instance
                    else:
                        logger.warning(f"未找到规则类: rule_key={rule_key}")

                except Exception as e:
                    logger.error(f"创建规则实例失败: rule_key={rule_key}, error={e}")

            logger.info(f"内存缓存更新完成: loaded_rules={len(RULE_CACHE)}")

        except Exception as e:
            logger.error(f"更新内存缓存失败: error={e}")
            raise
